<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.werewolf.activity.http.api.dao.Periodic.PeriodicActivityTeamRankDao">
    <select id="selectCurrentActivity" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(activity_id),0)
        FROM activity_periodic_team_rank_config
    </select>
    <select id="selectActivity" resultType="com.mega.werewolf.activity.common.entity.ActivityIndex">
        SELECT a.*
        FROM activity_index  a
        WHERE id = #{activityId}
    </select>
    <insert id="insertTeamSummaryRecordList">
        <if test="recordList != null and recordList.size() > 0">
            INSERT INTO activity_periodic_team_rank_summary_record (`rank_config_id`, `rank_type`, `team_index`, `team_id`, `is_light`, `team_exp`, `user_id`, `is_owner`, `user_exp`, `user_index`, `award_id`, `award_name`, `award_item_dic_id`, `award_num`, `has_receive`, `has_pass`, `receive_condation`, `create_time`, `update_time`, `delsign`)
            VALUES
            <foreach collection="recordList" item="item" separator=",">
                (#{item.rankConfigId}, #{item.rankType}, #{item.teamIndex}, #{item.teamId}, #{item.isLight}, #{item.teamExp}, #{item.userId}, #{item.isOwner}, #{item.userExp}, #{item.userIndex}, #{item.awardId}, #{item.awardName}, #{item.awardItemDicId}, #{item.awardNum}, #{item.hasReceive}, #{item.hasPass}, #{item.receiveCondation}, #{item.createTime}, #{item.updateTime}, #{item.delsign})
            </foreach>
        </if>
    </insert>
    <insert id="insertRankRecordList">
        INSERT INTO activity_periodic_team_rank_record (`rank_config_id`, `version_id`, `index`, `team_id`, `is_light`, `team_owner_id`, `exp`, `create_date`, `create_time`, `delsign`)
        VALUES
        <foreach collection="rankRecordList" item="item" separator=",">
            (#{item.rankConfigId}, #{item.versionId}, #{item.index}, #{item.teamId}, #{item.isLight}, #{item.teamOwnerId}, #{item.exp}, #{item.createDate}, #{item.createTime}, #{item.delsign})
        </foreach>
    </insert>
    <insert id="insertActivityCommReceiveRecordList">
        INSERT INTO `werewolf`.`activity_comm_award_team_receive_record` (`user_id`, `team_id`, `is_owner`, `activity_id`, `award_id`, `cost_coin`, `create_time`, `delsign`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.teamId}, #{item.isOwner}, #{item.activityId}, #{item.awardId}, #{item.costCoin}, #{item.createTime}, #{item.delsign})
        </foreach>
    </insert>
    <update id="updateReceiveSummaryRecordList">
        UPDATE activity_periodic_team_rank_summary_record SET has_receive = 1 WHERE id IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateRankConfigStateAndClick">
        UPDATE activity_periodic_team_rank_config SET can_click = #{canClick}, state = #{state} WHERE id = #{configId}
    </update>
    <update id="updateUndelsignRankRecord">
        UPDATE activity_periodic_team_rank_record SET delsign = 1 WHERE rank_config_id = #{configId}
    </update>
    <update id="updateRankConfigComplete">
        UPDATE activity_periodic_team_rank_config SET state = 2 WHERE id = #{rankConfigId}
    </update>

    <select id="selectRankDetailDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamRankAtyExpRankDetailDTO">
        SELECT
            rr.rank_config_id AS rankConfigId,
            rr.team_id AS teamId,
            rr.`index` AS teamIndex,
            t.`name` AS teamName,
            t.`level` AS teamLevel,
            t.is_light AS teamIsLight,
            rr.exp AS teamExp,
            rr.team_owner_id AS teamOwnerId,
            u.nickname AS teamOwnerNickName,
            u.headicon AS teamOwnerHeadIcon,
            IFNULL( u.frame, 0 ) AS teamOwnerFrame,
            IFNULL( un.noble_no, 0 ) AS teamOwnerNoble,
            IF
                ( un.noble_date >= CURRENT_DATE, 1, 0 ) AS teamOwnerNobleIsLight,
            IFNULL( ttr.id, 0 ) AS teamTagId
        FROM
            activity_periodic_team_rank_record rr
                LEFT JOIN team t ON rr.team_id = t.id
                LEFT JOIN tuser u ON rr.team_owner_id = u.`no`
                LEFT JOIN user_noble un ON u.`no` = un.user_no
                LEFT JOIN tag ttr ON t.id = ttr.team_id AND ttr.delsign = 0
                LEFT JOIN activity_periodic_team_rank_config AS amtrc ON amtrc.id = rr.rank_config_id
        WHERE
            rr.delsign = 0
          AND t.delsign = 0
          AND amtrc.activity_id = #{activityId}
        ORDER BY
            rr.rank_config_id ASC,
            rr.`index` ASC,
            rr.team_id ASC
    </select>

    <select id="selectAllRankConfigList"
            resultType="com.mega.werewolf.activity.common.entity.ActivityPeriodicTeamRankConfig">
        SELECT * FROM activity_periodic_team_rank_config WHERE activity_id = #{activityId}
    </select>

    <select id="selectMyTeamDetailDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamRankAtyMyTeamDetailDTO">
        SELECT
            t.id AS teamId,
            ui.isOwner AS isOwner,
            t.`name` AS teamName,
            t.`level` AS teamLevel,
            t.is_light AS teamIsLight,
            IFNULL(SUM(ter.exp),0) AS teamExp,
            t.owner_id AS teamOwnerId,
            u.nickname AS teamOwnerNickName,
            u.headicon AS teamOwnerHeadIcon,
            u.frame AS teamOwnerFrame,
            IFNULL( un.noble_no, 0 ) AS teamOwnerNoble,
            IF( un.noble_date >= CURRENT_DATE, 1, 0 ) AS teamOwnerNobleIsLight,
            IFNULL(ttr.id, 0) AS teamTagId
        FROM (SELECT tu.team_id AS teamId,
                     0 AS isOwner
              FROM team_user tu
              WHERE tu.user_id = #{userId} AND tu.delsign = 0
              UNION ALL
              SELECT t.id AS teamId,
                     1 AS isOwner
              FROM team t
              WHERE t.owner_id = #{userId} AND t.delsign = 0) ui
                 LEFT JOIN team t ON ui.teamId = t.id
                 LEFT JOIN tuser u ON t.owner_id = u.`no`
                 LEFT JOIN user_noble un ON u.`no` = un.user_no
                 LEFT JOIN team_exp_record ter ON ui.teamId = ter.team_id AND ter.create_time BETWEEN #{startTime} AND #{endTime}
                 LEFT JOIN tag ttr ON t.id = ttr.team_id AND ttr.delsign = 0
        GROUP BY ui.teamId
    </select>
    
    <select id="selectUnReceiveAwardNum" resultType="java.lang.Integer">
        SELECT 
            IFNULL(COUNT(*),0) 
        FROM activity_periodic_team_rank_summary_record sr
        LEFT JOIN activity_periodic_team_rank_config rc ON sr.rank_config_id = rc.id
        WHERE sr.user_id = #{userId} 
        AND rc.type = #{rankType} 
        AND sr.has_receive = 0 
        AND sr.delsign = 0
        AND rc.activity_id = #{activityId}
    </select>

    <select id="selectUnFinishRankConfig"
            resultType="com.mega.werewolf.activity.common.entity.ActivityPeriodicTeamRankConfig">
        SELECT * FROM activity_periodic_team_rank_config
        WHERE state != 2 
        AND activity_id = #{activityId}
        ORDER BY id ASC
    </select>

    <select id="selectRankConfig"
            resultType="com.mega.werewolf.activity.common.entity.ActivityPeriodicTeamRankConfig">
        SELECT * FROM activity_periodic_team_rank_config WHERE id = #{rankConfigId}
    </select>

    <select id="selectTeamUserDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamRankAtyUserDetailDTO">
        SELECT tu.user_id AS userId,
               u.nickname AS nickName,
               u.headicon AS headIcon,
               u.frame AS frame,
               IFNULL(SUM(ter.exp),0) AS exp,
               IFNULL( un.noble_no, 0 ) AS noble,
               IF( un.noble_date >= CURRENT_DATE, 1, 0 ) AS isLight
        FROM team_user tu
                 LEFT JOIN team_user_exp_record ter ON ter.user_id = tu.user_id AND ter.team_id = tu.team_id AND ter.create_time BETWEEN #{startTime} AND #{endTime}
                 LEFT JOIN tuser u ON tu.user_id = u.`no`
                 LEFT JOIN user_noble un ON un.user_no = tu.user_id
        WHERE tu.team_id = #{teamId} AND tu.delsign = 0
        GROUP BY tu.user_id
        ORDER BY exp DESC, u.`no` ASC
            LIMIT 10
    </select>
    <select id="selectMyTeamExpDetailDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamRankAtyMyTeamExpDTO">
        (SELECT
             t.id AS teamId,
             t.`name` AS teamName,
             1 AS isOwner,
             IFNULL( SUM( ter.exp ), 0 ) AS conExp,
             MAX( ter.create_time ) AS lastTime,
             0 AS myExp
         FROM
             team t
                 LEFT JOIN team_exp_record ter ON t.id = ter.team_id
                 AND ter.user_id = #{userId}
         WHERE
             t.owner_id = #{userId}
           AND t.delsign = 0
           AND ter.create_time BETWEEN #{startDateStr}
             AND #{endDateStr}) UNION ALL
        (SELECT
             tu.team_id AS teamId,
             t.`name` AS teamName,
             0 AS isOwner,
             IFNULL( SUM( ter.exp ), 0 ) AS conExp,
             IFNULL(
                     MAX( ter.create_time ),
                     NOW()) AS lastTime,
             0 AS myExp
         FROM
             team_user tu
                 LEFT JOIN team t ON tu.team_id = t.id
                 LEFT JOIN team_exp_record ter ON tu.team_id = ter.team_id
                 AND ter.user_id = #{userId}
                 AND ter.create_time BETWEEN #{startDateStr}
                                                      AND #{endDateStr}
         WHERE
             tu.user_id = #{userId}
           AND tu.delsign = 0
         GROUP BY
             tu.team_id,
             tu.user_id) UNION ALL
        (SELECT
             tu.team_id AS teamId,
             t.`name` AS teamName,
             0 AS isOwner,
             0 AS conExp,
             IFNULL(
                     MAX( tuer.create_time ),
                     NOW()) AS lastTime,
             IFNULL( SUM( tuer.exp ), 0 ) AS myExp
         FROM
             team_user tu
                 LEFT JOIN team t ON tu.team_id = t.id
                 LEFT JOIN team_user_exp_record tuer ON tuer.team_id = tu.team_id
                 AND tuer.user_id = #{userId}
                 AND tuer.create_time BETWEEN #{startDateStr}
                                                            AND #{endDateStr}
         WHERE
             tu.user_id = #{userId}
           AND tu.delsign = 0
         GROUP BY
             tu.team_id,
             tu.user_id)
    </select>

    <select id="selectUnReceiveSummaryRecordList"
            resultType="com.mega.werewolf.activity.common.entity.ActivityPeriodicTeamRankSummaryRecord">
        SELECT
            sr.*
        FROM
            activity_periodic_team_rank_summary_record AS sr
        LEFT JOIN activity_periodic_team_rank_config AS rc ON sr.rank_config_id = rc.id
        WHERE
            sr.user_id = #{userId}
        AND sr.rank_type = #{rankType}
        AND sr.delsign = 0
        AND rc.activity_id = #{activityId}
        ORDER BY sr.has_receive DESC
    </select>

    <select id="selectRankSummaryDetailDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamRankAtyTeamSummaryDetailDTO">
        SELECT rr.team_id AS teamId,
        rr.is_light AS isLight,
        rr.`index` AS teamIndex,
        rr.exp AS teamExp,
        rr.team_owner_id AS teamOwnerId,
        IFNULL(tu.user_id, 0) AS userId,
        IFNULL(SUM(tuer.exp),0) AS userExp
        FROM activity_periodic_team_rank_record rr
        LEFT JOIN team t ON rr.team_id = t.id
        LEFT JOIN team_user tu ON rr.team_id = tu.team_id AND tu.delsign = 0
        LEFT JOIN team_user_exp_record tuer ON tu.team_id = tuer.team_id AND tu.user_id = tuer.user_id AND tuer.create_time BETWEEN #{startDate} AND #{endDate}
        WHERE rr.rank_config_id = #{rankConfigId}
        <if test="rankType == 0">
            AND rr.`index` &lt;= 10
        </if>
        <if test="rankType == 1">
            AND rr.`index` &lt;= 6
        </if>
        AND rr.delsign = 0
        GROUP BY rr.team_id, tu.user_id
        ORDER BY rr.`index` ASC, userExp DESC, userId ASC
    </select>
    <select id="selectParticipateInSummaryDetailDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamRankAtyTeamSummaryDetailDTO">
        SELECT pi.teamId AS teamId,
        0 AS teamIndex,
        pi.isLight AS isLight,
        pi.exp AS teamExp,
        pi.ownerId AS teamOwnerId,
        IFNULL(tu.user_id,0) AS userId,
        IFNULL(SUM(tuer.exp),0) AS userExp
        FROM (SELECT ter.team_id AS teamId,
        t.is_light AS isLight,
        SUM(ter.exp) AS exp,
        t.owner_id AS ownerId
        FROM team_exp_record ter
        LEFT JOIN team t ON ter.team_id = t.id
        LEFT JOIN tuser_inner_total tit ON tit.user_id = t.owner_id
        WHERE ter.team_id NOT IN
        <foreach collection="rankList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

        <choose>
            <when test="env == 'beta'">
                AND tit.user_id IS NOT NULL
            </when>
            <otherwise>
                AND tit.user_id IS NULL
            </otherwise>
        </choose>
        
        AND ter.create_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY ter.team_id
        HAVING exp >= 500 ) pi
        LEFT JOIN team_user tu ON pi.teamId = tu.team_id AND tu.delsign = 0
        LEFT JOIN team_user_exp_record tuer ON tu.team_id = tuer.team_id AND tu.user_id = tuer.user_id AND tuer.create_time BETWEEN #{startDate} AND #{endDate}
        WHERE pi.ownerId IS NOT NULL
        GROUP BY pi.teamId, tu.user_id
        ORDER BY teamExp DESC, userExp DESC
    </select>
    
    <select id="selectCurrentMaxVersion" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(version_id),0) FROM activity_periodic_team_rank_record WHERE rank_config_id = #{configId}
    </select>

    <select id="selectTeamExpRankList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamExpRecordDTO">
        SELECT
            ter.team_id AS teamId,
            SUM(ter.exp) AS exp,
            t.owner_id AS ownerId,
            t.is_light AS isLight
        FROM team_exp_record  ter
                 LEFT JOIN team t ON ter.team_id = t.id
                 LEFT JOIN tuser_inner_total tit ON t.owner_id = tit.user_id
        WHERE tit.user_id IS NULL AND ter.create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY ter.team_id
        ORDER BY exp DESC, ter.team_id ASC
            LIMIT 30
    </select>

    <select id="selectAllUnReceiveAwardNum" resultType="java.lang.Integer">
        SELECT IFNULL(COUNT(*),0) 
        FROM activity_periodic_team_rank_summary_record sr
        LEFT JOIN activity_periodic_team_rank_config rc ON sr.rank_config_id = rc.id
        WHERE 
            sr.user_id = #{userId} 
        AND sr.has_receive = 0 
        AND sr.delsign = 0
        AND rc.activity_id = #{activityId}
    </select>

    <select id="selectActivityUserCompleteDetailInRegionNoGroup"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityTeamCompleteDetailDTO">
        SELECT
            acac.activity_id activityId,
            acac.award_id awardId,
            acac.props_no propsNo,
            acac.props_type propsType,
            acac.is_limit isLimit,
            acac.target_num targetNum,
            acac.max_times maxTimes,
            COUNT( acrr.create_time ) receiveCount,
            acac.item_dic_id itemDicId,
            acac.num num,
            GROUP_CONCAT(acrr.team_id) AS teamIdStr
        FROM
            activity_comm_award_conf acac
                LEFT JOIN activity_comm_award_team_receive_record acrr ON acac.activity_id = acrr.activity_id AND acrr.delsign = 0
                AND acac.award_id = acrr.award_id
                AND acrr.user_id = #{userId}
        WHERE
            acac.activity_id = #{activityid}
          AND acac.award_id BETWEEN #{start} AND #{end}
        GROUP BY acac.id
    </select>
    <select id="selectActivityUserCompleteDetailNoGroup"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityTeamCompleteDetailDTO">
        SELECT
            acac.activity_id activityId,
            acac.award_id awardId,
            acac.props_no propsNo,
            acac.props_type propsType,
            acac.is_limit isLimit,
            acac.target_num targetNum,
            acac.max_times maxTimes,
            COUNT( acrr.create_time ) receiveCount,
            acac.item_dic_id itemDicId,
            acac.num num,
            acac.sub_item_dic_id_list subItemDicIdList,
            GROUP_CONCAT(acrr.team_id) AS teamIdStr
        FROM
            activity_comm_award_conf acac
                LEFT JOIN activity_comm_award_team_receive_record acrr ON acac.activity_id = acrr.activity_id AND acrr.delsign = 0
                AND acac.award_id = acrr.award_id
                AND acrr.user_id = #{userId}
        WHERE
            acac.activity_id = #{activityId}
          AND acac.award_id = #{awardId}
        GROUP BY
            acac.id
    </select>
    <select id="selectUserTeamExpList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamUserExpDetailDTO">
        SELECT
        team_id AS teamId,
        SUM(exp) AS userTeamExp
        FROM team_user_exp_record WHERE user_id = #{userId} AND team_id IN
        <foreach collection="teamIdList" item="teamId" open="(" close=")" separator=",">
            #{teamId}
        </foreach>
        AND create_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY team_id
    </select>
    <select id="selectTeamExpDTOList"
            resultType="com.mega.werewolf.activity.http.api.dto.ActivityCommTeamExpDTO">
        SELECT
        team_id AS teamId,
        IFNULL(SUM(exp),0) AS exp
        FROM team_exp_record WHERE team_id IN
        <foreach collection="teamIdList" item="teamId" separator="," open="(" close=")">
            #{teamId}
        </foreach>
        AND create_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY team_id
    </select>

    <!-- selectTeramRankConfigList --> 
    <select id="selectTeramRankConfigList" resultType="com.mega.werewolf.activity.common.entity.TeamRankActivityConfig">
        SELECT * FROM team_rank_activity_config
    </select>


    <!-- selectTeamRankRecordList --> 

    <select id="selectTeamRankRecordList" resultType="com.mega.werewolf.activity.common.entity.ActivityPeriodicTeamRankRecord">
        SELECT
            a.*
        FROM
            activity_periodic_team_rank_record AS a
        WHERE
            a.rank_config_id = #{rankConfigId}
        AND a.id > #{lastId}
        AND a.delsign = 0
        ORDER BY a.id 
    </select>    

    <!-- selectLastId --> 

    <select id="selectLastId" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(source_id),0) 
        FROM team_rank_activity_record AS a
        WHERE a.team_activity_id = #{teamActivityId}
    </select>

    <!-- selectTeamActivityId -->

    <select id="selectTeamActivityId" resultType="java.lang.Integer">
        SELECT IFNULL(id,0)
        FROM team_rank_activity_config AS a
        WHERE a.activity_id = #{activityId}
    </select>

    <!-- insertTeamRankActivityRecordList --> 

    <insert id="insertTeamRankActivityRecordList">
        INSERT IGNORE INTO 
        team_rank_activity_record (team_activity_id, source_id, `index`, team_id, team_owner_id, exp, create_date, create_time, delsign, is_light, version_id)
        VALUES
        <foreach collection="recordList" item="item" separator="," open="" close="">
            (#{item.teamActivityId}, 
            #{item.sourceId}, 
            #{item.index}, 
            #{item.teamId}, 
            #{item.teamOwnerId}, 
            #{item.exp}, 
            #{item.createDate}, 
            #{item.createTime}, 
            #{item.delsign}, 
            #{item.isLight}, 
            #{item.versionId})
        </foreach>
    </insert>

    <!-- deleteTeamRankRecordList --> 

    <update id="deleteTeamRankRecordList">
        update team_rank_activity_record set delsign = 1 where team_activity_id = #{teamActivityId}
    </update>

    <select id="selectAwardSubItemDicId" resultType="java.lang.String">
        SELECT sub_item_dic_id_list
        FROM activity_comm_award_conf
        WHERE activity_id = #{activityId} AND award_id= #{awardId}
    </select>

    <select id="selectActivityType" resultType="java.lang.Integer">
        SELECT aty_type
        FROM activity_index AS a
        WHERE a.id = #{activityId}
    </select>
    
</mapper>